# Define key biblical events and estimated timeframes (in BCE/CE)
events = {
    "Creation of Adam": -4000,  # Estimated around 4000 BCE
    "Flood (Noah)": -2348,  # Estimated around 2348 BCE
    "<PERSON>'s Birth": -2000,  # Estimated around 2000 BCE
    "Exodus (<PERSON>)": -1446,  # Estimated around 1446 BCE
    "Birth of Jesus Christ": -4,  # Estimated around 4 BCE
    "Present Day": 2025  # Present year
}

# Define the number of generations for each period
generations = {
    "Adam to Noah": 10,  # 10 generations from <PERSON> to <PERSON> (Genesis 5)
    "<PERSON> to <PERSON>": 10,  # 10 generations from <PERSON> to <PERSON> (Genesis 11)
    "<PERSON> to <PERSON>": 28,  # 28 generations from <PERSON> to <PERSON> (<PERSON> & <PERSON>)
}

# Calculate generations from Jesus to the present
years_since_jesus = events["Present Day"] - events["Birth of Jesus Christ"]
average_generation_length = 40  # Average generation length in years
generations_since_jesus = years_since_jesus // average_generation_length

# Total generations from <PERSON> to the present
total_generations = (generations["Adam to Noah"] + 
                     generations["Noah to <PERSON>"] + 
                     generations["<PERSON> to <PERSON>"] + 
                     generations_since_jesus)

# Print the results
print("Generations Calculation:")
print(f"From Adam to Noah: {generations['Adam to Noah']} generations")
print(f"From <PERSON> to <PERSON>: {generations['Noah to <PERSON>']} generations")
print(f"From <PERSON> to <PERSON>: {generations['<PERSON> to <PERSON>']} generations")
print(f"From Jesus to Present: {generations_since_jesus} generations")
print(f"\nTotal generations from Adam to Present: {total_generations} generations")

# Optionally, we can also visualize this information in a bar chart
import matplotlib.pyplot as plt

# Data for plotting
generation_labels = ["Adam to Noah", "Noah to <PERSON>", "Abraham to Jesus", "Jesus to Present"]
generation_values = [
    generations["Adam to Noah"], 
    generations["Noah to Abraham"], 
    generations["Abraham to Jesus"], 
    generations_since_jesus
]

# Plotting the bar chart
plt.figure(figsize=(10, 6))
plt.bar(generation_labels, generation_values, color='skyblue')
plt.title('Generations from Adam to Present')
plt.xlabel('Periods')
plt.ylabel('Number of Generations')
plt.grid(True)
plt.show()
